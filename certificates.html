<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Educasheer Certificate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;600;700;800&family=Crimson+Text:wght@400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0a0f1c 0%, #1a1f3a 25%, #2d1b69 50%, #1e3a8a 75%, #1e40af 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .certificate-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(25px);
            width: 100%;
            max-width: 900px;
            aspect-ratio: 1.414;
            border-radius: 28px;
            box-shadow: 0 50px 120px rgba(0, 0, 0, 0.4),
                        0 25px 60px rgba(59, 130, 246, 0.2),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .certificate-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #fefefe 0%, #f8fafc 30%, #f1f5f9 70%, #e2e8f0 100%);
            opacity: 0.8;
        }

        .certificate-border {
            position: absolute;
            top: 25px;
            left: 25px;
            right: 25px;
            bottom: 25px;
            border: 3px solid transparent;
            border-radius: 20px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4, #8b5cf6) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            animation: borderGlow 4s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .certificate-content {
            position: relative;
            z-index: 2;
            padding: 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .logo-section {
            margin-bottom: 25px;
        }
        
        .logo {
            width: 110px;
            height: 110px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #a855f7 50%, #3b82f6 75%, #06b6d4 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 20px 50px rgba(168, 85, 247, 0.4),
                        0 10px 25px rgba(59, 130, 246, 0.3),
                        inset 0 2px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .logo::before {
            content: "𝓔";
            color: white;
            font-size: 52px;
            font-weight: 800;
            font-family: 'Crimson Text', serif;
            text-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
            z-index: 2;
            position: relative;
        }

        .logo::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: logoShine 4s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes logoShine {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        .company-name {
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(135deg, #1e1b4b, #3730a3, #7c3aed, #2563eb, #0891b2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Crimson Text', serif;
            letter-spacing: 4px;
            text-shadow: 0 3px 15px rgba(59, 130, 246, 0.4);
            position: relative;
        }

        .company-name::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
            border-radius: 2px;
            animation: companyGlow 3s ease-in-out infinite;
        }

        @keyframes companyGlow {
            0%, 100% { opacity: 1; box-shadow: 0 0 10px rgba(168, 85, 247, 0.5); }
            50% { opacity: 0.7; box-shadow: 0 0 20px rgba(59, 130, 246, 0.7); }
        }

        .certificate-title {
            font-size: 20px;
            color: #475569;
            font-weight: 600;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 5px;
            position: relative;
            font-family: 'Inter', sans-serif;
        }

        .certificate-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
            border-radius: 2px;
        }
        
        .recipient-section {
            margin-bottom: 25px;
        }
        
        .recipient-name {
            font-size: 52px;
            font-weight: 700;
            color: #1e293b;
            font-family: 'Crimson Text', serif;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
            text-shadow: 0 2px 8px rgba(30, 41, 59, 0.1);
        }

        .recipient-name::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
            border-radius: 2px;
            animation: nameGlow 2s ease-in-out infinite;
        }

        @keyframes nameGlow {
            0%, 100% { box-shadow: 0 0 15px rgba(168, 85, 247, 0.4); }
            50% { box-shadow: 0 0 25px rgba(59, 130, 246, 0.6); }
        }

        .completion-text {
            font-size: 20px;
            color: #475569;
            margin-bottom: 25px;
            line-height: 1.6;
            font-weight: 500;
            font-family: 'Inter', sans-serif;
        }

        .course-name {
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(135deg, #1e1b4b, #7c3aed, #2563eb, #0891b2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Crimson Text', serif;
            margin-bottom: 30px;
            position: relative;
            display: inline-block;
            text-shadow: 0 2px 10px rgba(124, 58, 237, 0.2);
        }

        .course-name::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 70%;
            height: 3px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
            border-radius: 2px;
        }
        
        .stars {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 25px;
        }
        
        .star {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            animation: sparkle 3s ease-in-out infinite;
            filter: drop-shadow(0 6px 12px rgba(251, 191, 36, 0.4));
            position: relative;
        }

        .star::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.6), transparent);
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
        }

        .star:nth-child(1) { animation-delay: 0s; }
        .star:nth-child(2) { animation-delay: 0.6s; }
        .star:nth-child(3) { animation-delay: 1.2s; }
        .star:nth-child(4) { animation-delay: 1.8s; }
        .star:nth-child(5) { animation-delay: 2.4s; }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); filter: drop-shadow(0 6px 12px rgba(251, 191, 36, 0.4)); }
            50% { transform: scale(1.3) rotate(180deg); filter: drop-shadow(0 8px 20px rgba(251, 191, 36, 0.6)); }
        }
        
        .supervisor-section {
            background: linear-gradient(135deg, rgba(168, 85, 247, 0.12), rgba(59, 130, 246, 0.12), rgba(6, 182, 212, 0.08));
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid rgba(168, 85, 247, 0.2);
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
        }

        .supervisor-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 16px;
        }

        .supervisor-title {
            font-size: 15px;
            color: #64748b;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .supervisor-name {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #1e1b4b, #7c3aed, #2563eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Crimson Text', serif;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .supervisor-title-role {
            font-size: 18px;
            color: #475569;
            font-weight: 600;
            font-style: italic;
            position: relative;
            z-index: 1;
        }
        
        .footer-section {
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding-top: 25px;
            border-top: 2px solid rgba(168, 85, 247, 0.2);
            position: relative;
        }

        .footer-section::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
        }

        .date {
            font-size: 18px;
            color: #475569;
            font-weight: 600;
            font-family: 'Inter', sans-serif;
        }

        .signature-section {
            text-align: center;
        }

        .signature-line {
            width: 220px;
            height: 3px;
            background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
            margin-bottom: 10px;
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
        }

        .signature-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .decorative-circle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            filter: blur(1px);
        }
        
        .decorative-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: -100px;
            right: -100px;
            animation: float 6s ease-in-out infinite;
        }
        
        .decorative-circle:nth-child(2) {
            width: 120px;
            height: 120px;
            bottom: -60px;
            left: -60px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .decorative-circle:nth-child(3) {
            width: 80px;
            height: 80px;
            top: 30%;
            right: 30px;
            opacity: 0.5;
            animation: float 10s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .editable {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            padding: 4px 12px;
            border-radius: 6px;
            font-weight: 600;
            color: #92400e;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(251, 191, 36, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .editable::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }
        
        .editable:hover {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
        }
        
        .editable:hover::before {
            left: 100%;
        }
        
        @media (max-width: 768px) {
            .certificate-content {
                padding: 30px;
            }
            
            .company-name {
                font-size: 26px;
            }
            
            .recipient-name {
                font-size: 36px;
            }
            
            .course-name {
                font-size: 24px;
            }
            
            .certificate-title {
                font-size: 16px;
            }
            
            .footer-section {
                flex-direction: column;
                gap: 20px;
            }
        }
        
        .certificate-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 50px 120px rgba(0, 0, 0, 0.4);
        }
        
        .certificate-container {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate-bg"></div>
        <div class="certificate-border"></div>
        <div class="decorative-elements">
            <div class="decorative-circle"></div>
            <div class="decorative-circle"></div>
            <div class="decorative-circle"></div>
        </div>
        
        <div class="certificate-content">
            <div class="logo-section">
                <div class="logo"></div>
                <div class="company-name">EDUCASHEER</div>
            </div>
            
            <div class="certificate-title">Certificate of Completion</div>
            
            <div class="recipient-section">
                <div class="recipient-name">
                    <span class="editable" onclick="editField(this)">John Doe</span>
                </div>
            </div>
            
            <div class="completion-text">
                has successfully completed the course
            </div>
            
            <div class="course-name">
                <span class="editable" onclick="editField(this)">Advanced Web Development</span>
            </div>
            
            <div class="stars">
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
            </div>
            
            <div class="supervisor-section">
                <div class="supervisor-title">Under the supervision of</div>
                <div class="supervisor-name">Musavir Khaliq</div>
                <div class="supervisor-title-role">Research Scientist</div>
            </div>
            
            <div class="footer-section">
                <div class="date">
                    Completed on: <span class="editable" onclick="editField(this)">December 15, 2024</span>
                </div>
                <div class="signature-section">
                    <div class="signature-line"></div>
                    <div class="signature-label">Authorized Signature</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function editField(element) {
            const currentText = element.textContent;
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentText;
            input.style.background = 'white';
            input.style.border = '2px solid #3b82f6';
            input.style.borderRadius = '6px';
            input.style.padding = '4px 12px';
            input.style.fontSize = window.getComputedStyle(element).fontSize;
            input.style.fontWeight = window.getComputedStyle(element).fontWeight;
            input.style.color = '#1e293b';
            input.style.textAlign = 'center';
            input.style.width = Math.max(250, currentText.length * 14) + 'px';
            input.style.outline = 'none';
            input.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
            
            element.parentNode.replaceChild(input, element);
            input.focus();
            input.select();
            
            function saveChanges() {
                const newSpan = document.createElement('span');
                newSpan.className = 'editable';
                newSpan.textContent = input.value || currentText;
                newSpan.onclick = function() { editField(this); };
                input.parentNode.replaceChild(newSpan, input);
            }
            
            input.addEventListener('blur', saveChanges);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveChanges();
                }
            });
        }
        
        // Enhanced interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const certificate = document.querySelector('.certificate-container');
            
            // Smooth hover effects
            certificate.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            certificate.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
            
            // Add parallax effect to decorative elements
            document.addEventListener('mousemove', function(e) {
                const circles = document.querySelectorAll('.decorative-circle');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                circles.forEach((circle, index) => {
                    const speed = (index + 1) * 0.5;
                    const xPos = (x - 0.5) * speed * 10;
                    const yPos = (y - 0.5) * speed * 10;
                    circle.style.transform = `translate(${xPos}px, ${yPos}px)`;
                });
            });
        });
    </script>
</body>
</html>