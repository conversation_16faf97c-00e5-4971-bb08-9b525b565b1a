<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Educasheer Certificate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .certificate-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            width: 100%;
            max-width: 900px;
            aspect-ratio: 1.414;
            border-radius: 24px;
            box-shadow: 0 40px 100px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .certificate-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            opacity: 0.6;
        }
        
        .certificate-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid transparent;
            border-radius: 16px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6, #3b82f6) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }
        
        .certificate-content {
            position: relative;
            z-index: 2;
            padding: 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .logo-section {
            margin-bottom: 25px;
        }
        
        .logo {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .logo::before {
            content: "E";
            color: white;
            font-size: 42px;
            font-weight: 700;
            font-family: 'Playfair Display', serif;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .logo::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .company-name {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(135deg, #1e40af, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Playfair Display', serif;
            letter-spacing: 3px;
            text-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
        }
        
        .certificate-title {
            font-size: 18px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 4px;
            position: relative;
        }
        
        .certificate-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        }
        
        .recipient-section {
            margin-bottom: 25px;
        }
        
        .recipient-name {
            font-size: 48px;
            font-weight: 700;
            color: #1e293b;
            font-family: 'Playfair Display', serif;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        .recipient-name::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 2px;
        }
        
        .completion-text {
            font-size: 18px;
            color: #475569;
            margin-bottom: 20px;
            line-height: 1.6;
            font-weight: 400;
        }
        
        .course-name {
            font-size: 32px;
            font-weight: 600;
            background: linear-gradient(135deg, #1e40af, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Playfair Display', serif;
            margin-bottom: 25px;
            position: relative;
            display: inline-block;
        }
        
        .course-name::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 2px;
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
        }
        
        .stars {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 25px;
        }
        
        .star {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            animation: sparkle 2s ease-in-out infinite;
            filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
        }
        
        .star:nth-child(1) { animation-delay: 0s; }
        .star:nth-child(2) { animation-delay: 0.4s; }
        .star:nth-child(3) { animation-delay: 0.8s; }
        .star:nth-child(4) { animation-delay: 1.2s; }
        .star:nth-child(5) { animation-delay: 1.6s; }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        .supervisor-section {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .supervisor-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 8px;
        }
        
        .supervisor-name {
            font-size: 24px;
            font-weight: 700;
            color: #1e40af;
            font-family: 'Playfair Display', serif;
            margin-bottom: 5px;
        }
        
        .supervisor-title-role {
            font-size: 16px;
            color: #475569;
            font-weight: 500;
            font-style: italic;
        }
        
        .footer-section {
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding-top: 20px;
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .date {
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
        }
        
        .signature-section {
            text-align: center;
        }
        
        .signature-line {
            width: 200px;
            height: 2px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            margin-bottom: 8px;
            border-radius: 1px;
        }
        
        .signature-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .decorative-circle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            filter: blur(1px);
        }
        
        .decorative-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: -100px;
            right: -100px;
            animation: float 6s ease-in-out infinite;
        }
        
        .decorative-circle:nth-child(2) {
            width: 120px;
            height: 120px;
            bottom: -60px;
            left: -60px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .decorative-circle:nth-child(3) {
            width: 80px;
            height: 80px;
            top: 30%;
            right: 30px;
            opacity: 0.5;
            animation: float 10s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .editable {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            padding: 4px 12px;
            border-radius: 6px;
            font-weight: 600;
            color: #92400e;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(251, 191, 36, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .editable::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }
        
        .editable:hover {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
        }
        
        .editable:hover::before {
            left: 100%;
        }
        
        @media (max-width: 768px) {
            .certificate-content {
                padding: 30px;
            }
            
            .company-name {
                font-size: 26px;
            }
            
            .recipient-name {
                font-size: 36px;
            }
            
            .course-name {
                font-size: 24px;
            }
            
            .certificate-title {
                font-size: 16px;
            }
            
            .footer-section {
                flex-direction: column;
                gap: 20px;
            }
        }
        
        .certificate-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 50px 120px rgba(0, 0, 0, 0.4);
        }
        
        .certificate-container {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate-bg"></div>
        <div class="certificate-border"></div>
        <div class="decorative-elements">
            <div class="decorative-circle"></div>
            <div class="decorative-circle"></div>
            <div class="decorative-circle"></div>
        </div>
        
        <div class="certificate-content">
            <div class="logo-section">
                <div class="logo"></div>
                <div class="company-name">EDUCASHEER</div>
            </div>
            
            <div class="certificate-title">Certificate of Completion</div>
            
            <div class="recipient-section">
                <div class="recipient-name">
                    <span class="editable" onclick="editField(this)">John Doe</span>
                </div>
            </div>
            
            <div class="completion-text">
                has successfully completed the course
            </div>
            
            <div class="course-name">
                <span class="editable" onclick="editField(this)">Advanced Web Development</span>
            </div>
            
            <div class="stars">
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
            </div>
            
            <div class="supervisor-section">
                <div class="supervisor-title">Under the supervision of</div>
                <div class="supervisor-name">Musavir Khaliq</div>
                <div class="supervisor-title-role">Research Scientist</div>
            </div>
            
            <div class="footer-section">
                <div class="date">
                    Completed on: <span class="editable" onclick="editField(this)">December 15, 2024</span>
                </div>
                <div class="signature-section">
                    <div class="signature-line"></div>
                    <div class="signature-label">Authorized Signature</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function editField(element) {
            const currentText = element.textContent;
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentText;
            input.style.background = 'white';
            input.style.border = '2px solid #3b82f6';
            input.style.borderRadius = '6px';
            input.style.padding = '4px 12px';
            input.style.fontSize = window.getComputedStyle(element).fontSize;
            input.style.fontWeight = window.getComputedStyle(element).fontWeight;
            input.style.color = '#1e293b';
            input.style.textAlign = 'center';
            input.style.width = Math.max(250, currentText.length * 14) + 'px';
            input.style.outline = 'none';
            input.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
            
            element.parentNode.replaceChild(input, element);
            input.focus();
            input.select();
            
            function saveChanges() {
                const newSpan = document.createElement('span');
                newSpan.className = 'editable';
                newSpan.textContent = input.value || currentText;
                newSpan.onclick = function() { editField(this); };
                input.parentNode.replaceChild(newSpan, input);
            }
            
            input.addEventListener('blur', saveChanges);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveChanges();
                }
            });
        }
        
        // Enhanced interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const certificate = document.querySelector('.certificate-container');
            
            // Smooth hover effects
            certificate.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            certificate.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
            
            // Add parallax effect to decorative elements
            document.addEventListener('mousemove', function(e) {
                const circles = document.querySelectorAll('.decorative-circle');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                circles.forEach((circle, index) => {
                    const speed = (index + 1) * 0.5;
                    const xPos = (x - 0.5) * speed * 10;
                    const yPos = (y - 0.5) * speed * 10;
                    circle.style.transform = `translate(${xPos}px, ${yPos}px)`;
                });
            });
        });
    </script>
</body>
</html>